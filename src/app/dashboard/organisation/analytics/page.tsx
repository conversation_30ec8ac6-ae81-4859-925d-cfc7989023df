'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { RoleGuard } from '@/components/auth/RoleGuard';
import { motion } from 'framer-motion';
import {
  BarChart3,
  TrendingUp,
  Eye,
  Users,
  Calendar,
  Download,
  BookOpen,
  Award,
  MessageSquare,
  Target,
  Clock,
  CheckCircle
} from 'lucide-react';
import toast from 'react-hot-toast';

interface AnalyticsData {
  overview: {
    totalJobs: number;
    totalApplications: number;
    totalCourses: number;
    totalStudents: number;
    totalEvents: number;
    totalCertificates: number;
    applicationRate: number;
    avgTimeToHire: number;
    successRate: number;
  };
  jobPerformance: {
    jobId: string;
    title: string;
    views: number;
    applications: number;
    conversionRate: number;
    status: string;
  }[];
  coursePopularity: {
    courseId: string;
    title: string;
    enrollments: number;
    completions: number;
    rating: number;
    category: string;
  }[];
  applicationTrends: {
    date: string;
    applications: number;
    views: number;
  }[];
  studentEngagement: {
    totalActiveStudents: number;
    avgSessionDuration: number;
    courseCompletionRate: number;
    certificateEarnRate: number;
  };
}

export default function EmployerAnalyticsPage() {
  const { data: session } = useSession();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30');

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);

  const fetchAnalyticsData = async () => {
    try {
      const response = await fetch(`/api/dashboard/organisation/analytics?days=${timeRange}`);
      const result = await response.json();

      if (result.success) {
        setAnalyticsData(result.data);
      } else {
        toast.error('Failed to fetch analytics data');
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Failed to fetch analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  const exportReport = async () => {
    try {
      const response = await fetch(`/api/dashboard/organisation/analytics/export?days=${timeRange}`);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics-report-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Report exported successfully');
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error('Failed to export report');
    }
  };

  if (isLoading) {
    return (
      <RoleGuard allowedRoles={['organisation']}>
        <div className="min-h-screen galaxy-background flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-galaxy-blue-light"></div>
        </div>
      </RoleGuard>
    );
  }

  return (
    <RoleGuard allowedRoles={['organisation']}>
      <div className="min-h-screen galaxy-background">
        <div className="star-field"></div>
        <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

        <div className="relative z-10 section-container py-8">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
            <div className="mb-4 lg:mb-0">
              <h1 className="text-3xl font-bold text-white galaxy-text-blue mb-2">
                Analytics Dashboard
              </h1>
              <p className="text-gray-300">
                Track your organization's performance and gain insights into recruitment, education, and engagement.
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:border-galaxy-blue focus:outline-none"
              >
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
              </select>
              <button
                onClick={exportReport}
                className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-300 glow-border"
              >
                <Download className="h-5 w-5 mr-2" />
                Export Report
              </button>
            </div>
          </div>

          {/* Key Metrics */}
          {analyticsData && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <div className="flex items-center">
                  <div className="p-3 bg-blue-600/20 rounded-lg">
                    <Users className="h-6 w-6 text-blue-400" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-300">Total Applications</p>
                    <p className="text-2xl font-bold text-white">{analyticsData.overview.totalApplications}</p>
                    <p className="text-sm text-blue-400 mt-1">
                      {analyticsData.overview.applicationRate.toFixed(1)}% conversion rate
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <div className="flex items-center">
                  <div className="p-3 bg-green-600/20 rounded-lg">
                    <BookOpen className="h-6 w-6 text-green-400" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-300">Active Students</p>
                    <p className="text-2xl font-bold text-white">{analyticsData.overview.totalStudents}</p>
                    <p className="text-sm text-green-400 mt-1">
                      {analyticsData.studentEngagement.courseCompletionRate.toFixed(1)}% completion rate
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <div className="flex items-center">
                  <div className="p-3 bg-yellow-600/20 rounded-lg">
                    <Clock className="h-6 w-6 text-yellow-400" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-300">Avg. Time to Hire</p>
                    <p className="text-2xl font-bold text-white">{analyticsData.overview.avgTimeToHire} days</p>
                    <p className="text-sm text-yellow-400 mt-1">Recruitment efficiency</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <div className="flex items-center">
                  <div className="p-3 bg-purple-600/20 rounded-lg">
                    <Award className="h-6 w-6 text-purple-400" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-300">Certificates Issued</p>
                    <p className="text-2xl font-bold text-white">{analyticsData.overview.totalCertificates}</p>
                    <p className="text-sm text-purple-400 mt-1">
                      {analyticsData.studentEngagement.certificateEarnRate.toFixed(1)}% earn rate
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          )}

          {/* Charts and Analytics */}
          {analyticsData && (
            <>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Course Popularity */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="muted-glassmorphic rounded-xl p-6 glow-border"
                >
                  <h3 className="text-lg font-semibold text-white mb-6">Most Popular Courses</h3>
                  <div className="space-y-4">
                    {analyticsData.coursePopularity.slice(0, 5).map((course, index) => (
                      <div key={course.courseId} className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                              {index + 1}
                            </div>
                            <div>
                              <p className="text-white font-medium">{course.title}</p>
                              <p className="text-gray-400 text-sm">{course.category}</p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-white font-semibold">{course.enrollments}</p>
                          <p className="text-gray-400 text-sm">enrollments</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>

                {/* Job Performance */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="muted-glassmorphic rounded-xl p-6 glow-border"
                >
                  <h3 className="text-lg font-semibold text-white mb-6">Top Performing Jobs</h3>
                  <div className="space-y-4">
                    {analyticsData.jobPerformance.slice(0, 5).map((job, index) => (
                      <div key={job.jobId} className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-green-600 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                              {index + 1}
                            </div>
                            <div>
                              <p className="text-white font-medium">{job.title}</p>
                              <p className="text-gray-400 text-sm">{job.conversionRate.toFixed(1)}% conversion</p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-white font-semibold">{job.applications}</p>
                          <p className="text-gray-400 text-sm">applications</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              </div>

              {/* Student Engagement Metrics */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border mb-8"
              >
                <h3 className="text-lg font-semibold text-white mb-6">Student Engagement Overview</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="h-8 w-8 text-blue-400" />
                    </div>
                    <p className="text-2xl font-bold text-white">{analyticsData.studentEngagement.totalActiveStudents}</p>
                    <p className="text-gray-300">Active Students</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Clock className="h-8 w-8 text-green-400" />
                    </div>
                    <p className="text-2xl font-bold text-white">{analyticsData.studentEngagement.avgSessionDuration}m</p>
                    <p className="text-gray-300">Avg. Session</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="h-8 w-8 text-purple-400" />
                    </div>
                    <p className="text-2xl font-bold text-white">{analyticsData.studentEngagement.courseCompletionRate.toFixed(1)}%</p>
                    <p className="text-gray-300">Completion Rate</p>
                  </div>
                </div>
              </motion.div>

              {/* Summary Insights */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <h3 className="text-lg font-semibold text-white mb-6">Key Insights</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-semibold text-galaxy-blue">Recruitment Performance</h4>
                    <ul className="space-y-2 text-gray-300">
                      <li className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-galaxy-blue" />
                        <span>Application rate: {analyticsData.overview.applicationRate.toFixed(1)}%</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-galaxy-blue" />
                        <span>Average time to hire: {analyticsData.overview.avgTimeToHire} days</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <TrendingUp className="h-4 w-4 text-galaxy-blue" />
                        <span>Success rate: {analyticsData.overview.successRate.toFixed(1)}%</span>
                      </li>
                    </ul>
                  </div>
                  <div className="space-y-4">
                    <h4 className="font-semibold text-galaxy-blue">Education Impact</h4>
                    <ul className="space-y-2 text-gray-300">
                      <li className="flex items-center space-x-2">
                        <BookOpen className="h-4 w-4 text-galaxy-blue" />
                        <span>{analyticsData.overview.totalCourses} active courses</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-galaxy-blue" />
                        <span>{analyticsData.overview.totalStudents} enrolled students</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <Award className="h-4 w-4 text-galaxy-blue" />
                        <span>{analyticsData.overview.totalCertificates} certificates issued</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </motion.div>
            </>
          )}
        </div>
      </div>
    </RoleGuard>
  );
}
