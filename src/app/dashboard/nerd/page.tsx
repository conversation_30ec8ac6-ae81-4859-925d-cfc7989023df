'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { RoleGuard } from '@/components/auth/RoleGuard';

interface DashboardStats {
  coursesEnrolled: number;
  coursesCompleted: number;
  certificatesEarned: number;
  jobApplications: number;
  mentorshipSessions: number;
  learningHours: number;
}

interface RecentActivity {
  id: string;
  type: 'course_enrolled' | 'course_completed' | 'job_applied' | 'session_booked' | 'certificate_earned';
  title: string;
  description: string;
  date: string;
  link?: string;
}

interface EnrolledCourse {
  _id: string;
  title: string;
  progress: number;
  instructor: string;
  thumbnail?: string;
  nextLesson?: string;
}

export default function NerdDashboard() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<DashboardStats>({
    coursesEnrolled: 0,
    coursesCompleted: 0,
    certificatesEarned: 0,
    jobApplications: 0,
    mentorshipSessions: 0,
    learningHours: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [enrolledCourses, setEnrolledCourses] = useState<EnrolledCourse[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsRes, coursesRes, activityRes] = await Promise.all([
        fetch('/api/dashboard/nerd/stats'),
        fetch('/api/dashboard/nerd/courses'),
        fetch('/api/dashboard/nerd/activity'),
      ]);

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData.data);
      }

      if (coursesRes.ok) {
        const coursesData = await coursesRes.json();
        setEnrolledCourses(coursesData.data);
      }

      if (activityRes.ok) {
        const activityData = await activityRes.json();
        setRecentActivity(activityData.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-galaxy-blue-light"></div>
      </div>
    );
  }

  return (
    <RoleGuard allowedRoles={['nerd']}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white galaxy-text-blue">
            Welcome back, {session?.user?.name}!
          </h1>
          <p className="text-gray-300 mt-2">
            Continue your learning journey and explore new opportunities.
          </p>
        </div>

          {/* Stats Grid - Bento Box Layout */}
          <div className="bento-grid bento-grid-features mb-8">
            <StatCard
              title="Courses Enrolled"
              value={stats.coursesEnrolled}
              icon="📚"
              link="/courses"
            />
            <StatCard
              title="Courses Completed"
              value={stats.coursesCompleted}
              icon="✅"
              link="/dashboard/nerd/completed"
            />
            <StatCard
              title="Certificates Earned"
              value={stats.certificatesEarned}
              icon="🏆"
              link="/certificates"
            />
            <StatCard
              title="Job Applications"
              value={stats.jobApplications}
              icon="💼"
              link="/jobs/applications"
            />
            <StatCard
              title="Mentorship Sessions"
              value={stats.mentorshipSessions}
              icon="👥"
              link="/mentorship/sessions"
            />
            <StatCard
              title="Learning Hours"
              value={stats.learningHours}
              icon="⏱️"
              suffix=" hrs"
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Continue Learning */}
            <div className="lg:col-span-2">
              <div className="muted-glassmorphic bento-item rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-white galaxy-text-purple">Continue Learning</h2>
                  <Link
                    href="/courses"
                    className="text-galaxy-blue-light hover:text-galaxy-purple text-sm font-medium transition-colors"
                  >
                    View All Courses
                  </Link>
                </div>

                {enrolledCourses.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-300 mb-4">You haven't enrolled in any courses yet.</p>
                    <Link
                      href="/courses"
                      className="inline-flex items-center px-4 py-2 bg-galaxy-blue text-white rounded-md hover:bg-galaxy-blue-light transition-colors glow-border"
                    >
                      Browse Courses
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {enrolledCourses.slice(0, 3).map((course) => (
                      <CourseCard key={course._id} course={course} />
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions & Recent Activity */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <div className="muted-glassmorphic bento-item rounded-lg p-6">
                <h2 className="text-xl font-semibold text-white galaxy-text-blue mb-4">Quick Actions</h2>
                <div className="space-y-3">
                  <QuickActionButton
                    href="/courses"
                    icon="📚"
                    title="Browse Courses"
                    description="Discover new learning opportunities"
                  />
                  <QuickActionButton
                    href="/jobs"
                    icon="💼"
                    title="Find Jobs"
                    description="Explore career opportunities"
                  />
                  <QuickActionButton
                    href="/mentorship"
                    icon="👥"
                    title="Find Mentor"
                    description="Get guidance from experts"
                  />
                  <QuickActionButton
                    href="/assessments"
                    icon="📝"
                    title="Take Assessment"
                    description="Test your skills"
                  />
                </div>
              </div>

              {/* Recent Activity */}
              <div className="muted-glassmorphic bento-item rounded-lg p-6">
                <h2 className="text-xl font-semibold text-white galaxy-text-pink mb-4">Recent Activity</h2>
                {recentActivity.length === 0 ? (
                  <p className="text-gray-300 text-sm">No recent activity</p>
                ) : (
                  <div className="space-y-3">
                    {recentActivity.slice(0, 5).map((activity) => (
                      <ActivityItem key={activity.id} activity={activity} />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
    </RoleGuard>
  );
}

function StatCard({
  title,
  value,
  icon,
  link,
  suffix = ''
}: {
  title: string;
  value: number;
  icon: string;
  link?: string;
  suffix?: string;
}) {
  const content = (
    <div className="muted-glassmorphic bento-item rounded-lg p-6 hover:glow-border transition-all duration-300">
      <div className="flex items-center">
        <div className="text-2xl mr-3">{icon}</div>
        <div>
          <p className="text-sm font-medium text-gray-300">{title}</p>
          <p className="text-2xl font-bold text-white">
            {value}{suffix}
          </p>
        </div>
      </div>
    </div>
  );

  return link ? <Link href={link}>{content}</Link> : content;
}

function CourseCard({ course }: { course: EnrolledCourse }) {
  return (
    <div className="flex items-center p-4 border border-white/10 rounded-lg hover:bg-white/5 transition-colors">
      <div className="flex-shrink-0 w-16 h-16 bg-galaxy-deep rounded-lg mr-4">
        {course.thumbnail ? (
          <img
            src={course.thumbnail}
            alt={course.title}
            className="w-full h-full object-cover rounded-lg"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-300">
            📚
          </div>
        )}
      </div>
      <div className="flex-1">
        <h3 className="font-medium text-white">{course.title}</h3>
        <p className="text-sm text-gray-300">by {course.instructor}</p>
        <div className="mt-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-300">Progress</span>
            <span className="font-medium text-white">{course.progress}%</span>
          </div>
          <div className="mt-1 bg-galaxy-deep rounded-full h-2">
            <div
              className="bg-galaxy-blue-light h-2 rounded-full"
              style={{ width: `${course.progress}%` }}
            />
          </div>
        </div>
      </div>
      <Link
        href={`/courses/${course._id}`}
        className="ml-4 px-4 py-2 bg-galaxy-blue text-white text-sm rounded-md hover:bg-galaxy-blue-light transition-colors glow-border"
      >
        Continue
      </Link>
    </div>
  );
}

function QuickActionButton({
  href,
  icon,
  title,
  description
}: {
  href: string;
  icon: string;
  title: string;
  description: string;
}) {
  return (
    <Link
      href={href}
      className="flex items-center p-3 border border-white/10 rounded-lg hover:bg-white/5 hover:glow-border transition-all duration-300"
    >
      <div className="text-xl mr-3">{icon}</div>
      <div>
        <p className="font-medium text-white">{title}</p>
        <p className="text-sm text-gray-300">{description}</p>
      </div>
    </Link>
  );
}

function ActivityItem({ activity }: { activity: RecentActivity }) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'course_enrolled': return '📚';
      case 'course_completed': return '✅';
      case 'job_applied': return '💼';
      case 'session_booked': return '👥';
      case 'certificate_earned': return '🏆';
      default: return '📝';
    }
  };

  const content = (
    <div className="flex items-start space-x-3">
      <div className="text-lg">{getActivityIcon(activity.type)}</div>
      <div className="flex-1">
        <p className="text-sm font-medium text-white">{activity.title}</p>
        <p className="text-xs text-gray-300">{activity.description}</p>
        <p className="text-xs text-gray-400 mt-1">
          {new Date(activity.date).toLocaleDateString()}
        </p>
      </div>
    </div>
  );

  return activity.link ? (
    <Link href={activity.link} className="block hover:bg-white/5 p-2 rounded transition-colors">
      {content}
    </Link>
  ) : (
    <div className="p-2">{content}</div>
  );
}