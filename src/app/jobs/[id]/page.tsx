'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import {
  MapPin,
  Clock,
  DollarSign,
  Building,
  Calendar,
  Users,
  Briefcase,
  ArrowLeft,
  ExternalLink,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Job {
  _id: string;
  title: string;
  company: string;
  companyLogo?: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  type: string;
  location: {
    type: string;
    city?: string;
    country?: string;
    address?: string;
  };
  salary: {
    min?: number;
    max?: number;
    currency: string;
    period: string;
  };
  category: string;
  skillsRequired: string[];
  experienceLevel: string;
  benefits: string[];
  applicationDeadline?: string;
  startDate?: string;
  screeningQuestions?: {
    question: string;
    type: 'text' | 'multiple_choice';
    options?: string[];
    required: boolean;
  }[];
  applications: {
    total: number;
  };
  status: string;
  createdAt: string;
}

interface ApplicationForm {
  coverLetter: string;
  resumeUrl: string;
  portfolioUrl: string;
  answers: { question: string; answer: string }[];
}

export default function JobDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [job, setJob] = useState<Job | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isApplying, setIsApplying] = useState(false);
  const [hasApplied, setHasApplied] = useState(false);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [applicationForm, setApplicationForm] = useState<ApplicationForm>({
    coverLetter: '',
    resumeUrl: '',
    portfolioUrl: '',
    answers: []
  });

  useEffect(() => {
    fetchJobDetails();
    if (session?.user?.id) {
      checkApplicationStatus();
    }
  }, [params.id, session]);

  const fetchJobDetails = async () => {
    try {
      const response = await fetch(`/api/jobs/${params.id}`);
      const result = await response.json();

      if (result.success) {
        setJob(result.data);
        // Initialize answers for screening questions
        if (result.data.screeningQuestions) {
          setApplicationForm(prev => ({
            ...prev,
            answers: result.data.screeningQuestions.map((q: any) => ({
              question: q.question,
              answer: ''
            }))
          }));
        }
      } else {
        toast.error('Job not found');
        router.push('/jobs');
      }
    } catch (error) {
      console.error('Error fetching job:', error);
      toast.error('Failed to load job details');
    } finally {
      setIsLoading(false);
    }
  };

  const checkApplicationStatus = async () => {
    try {
      const response = await fetch(`/api/jobs/${params.id}/apply`);
      if (response.ok) {
        setHasApplied(true);
      }
    } catch (error) {
      // User hasn't applied yet
    }
  };

  const handleApply = async () => {
    if (!session) {
      toast.error('Please sign in to apply for jobs');
      router.push('/auth/signin');
      return;
    }

    setIsApplying(true);
    try {
      const response = await fetch(`/api/jobs/${params.id}/apply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(applicationForm),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Application submitted successfully!');
        setHasApplied(true);
        setShowApplicationForm(false);
      } else {
        toast.error(result.error || 'Failed to submit application');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      toast.error('Failed to submit application');
    } finally {
      setIsApplying(false);
    }
  };

  const updateAnswer = (index: number, answer: string) => {
    setApplicationForm(prev => ({
      ...prev,
      answers: prev.answers.map((item, i) => 
        i === index ? { ...item, answer } : item
      )
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen galaxy-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-galaxy-blue-light"></div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="min-h-screen galaxy-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Job Not Found</h1>
          <button
            onClick={() => router.push('/jobs')}
            className="bg-galaxy-blue text-white px-6 py-2 rounded-lg hover:bg-galaxy-blue-light transition-colors"
          >
            Back to Jobs
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen galaxy-background">
      <div className="star-field"></div>
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>
      
      <div className="relative z-10 section-container py-8">
        {/* Back Button */}
        <button
          onClick={() => router.back()}
          className="flex items-center space-x-2 text-galaxy-blue hover:text-galaxy-blue-light transition-colors mb-6"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Back to Jobs</span>
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Job Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-xl glow-border">
                  {job.company.split(' ').map(word => word[0]).join('')}
                </div>
                <div className="flex-1">
                  <h1 className="text-3xl font-bold text-white font-orbitron mb-2">
                    {job.title}
                  </h1>
                  <div className="flex items-center space-x-4 text-gray-300 mb-4">
                    <div className="flex items-center space-x-1">
                      <Building className="h-4 w-4" />
                      <span>{job.company}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-4 w-4" />
                      <span>{job.location.city}, {job.location.country}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Briefcase className="h-4 w-4" />
                      <span className="capitalize">{job.type}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <span>{job.applications.total} applications</span>
                    <span>Posted {new Date(job.createdAt).toLocaleDateString()}</span>
                    {job.applicationDeadline && (
                      <span>Deadline: {new Date(job.applicationDeadline).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Job Description */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h2 className="text-xl font-semibold text-white mb-4">Job Description</h2>
              <div className="text-gray-300 whitespace-pre-wrap">
                {job.description}
              </div>
            </motion.div>

            {/* Requirements */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h2 className="text-xl font-semibold text-white mb-4">Requirements</h2>
              <ul className="space-y-2">
                {job.requirements.map((req, index) => (
                  <li key={index} className="flex items-start space-x-2 text-gray-300">
                    <CheckCircle className="h-4 w-4 text-galaxy-blue mt-1 flex-shrink-0" />
                    <span>{req}</span>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Responsibilities */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h2 className="text-xl font-semibold text-white mb-4">Responsibilities</h2>
              <ul className="space-y-2">
                {job.responsibilities.map((resp, index) => (
                  <li key={index} className="flex items-start space-x-2 text-gray-300">
                    <CheckCircle className="h-4 w-4 text-galaxy-blue mt-1 flex-shrink-0" />
                    <span>{resp}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Application Status */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              {hasApplied ? (
                <div className="text-center">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">Application Submitted</h3>
                  <p className="text-gray-300 text-sm">
                    Your application has been submitted successfully. You'll be notified of any updates.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">Apply for this position</h3>
                  <button
                    onClick={() => setShowApplicationForm(true)}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 glow-border"
                  >
                    Apply Now
                  </button>
                </div>
              )}
            </motion.div>

            {/* Job Details */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h3 className="text-lg font-semibold text-white mb-4">Job Details</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Salary</span>
                  <span className="text-white">
                    {job.salary.min && job.salary.max 
                      ? `${job.salary.currency} ${job.salary.min.toLocaleString()} - ${job.salary.max.toLocaleString()}`
                      : 'Competitive'
                    }
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Experience Level</span>
                  <span className="text-white capitalize">{job.experienceLevel}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Category</span>
                  <span className="text-white">{job.category}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Location Type</span>
                  <span className="text-white capitalize">{job.location.type}</span>
                </div>
              </div>
            </motion.div>

            {/* Skills Required */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h3 className="text-lg font-semibold text-white mb-4">Skills Required</h3>
              <div className="flex flex-wrap gap-2">
                {job.skillsRequired.map((skill, index) => (
                  <span
                    key={index}
                    className="bg-galaxy-blue/20 text-galaxy-blue px-3 py-1 rounded-full text-sm border border-galaxy-blue/30"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </motion.div>

            {/* Benefits */}
            {job.benefits.length > 0 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <h3 className="text-lg font-semibold text-white mb-4">Benefits</h3>
                <ul className="space-y-2">
                  {job.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start space-x-2 text-gray-300">
                      <CheckCircle className="h-4 w-4 text-galaxy-blue mt-1 flex-shrink-0" />
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Application Form Modal */}
      {showApplicationForm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-galaxy-deep border border-white/10 rounded-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <h2 className="text-2xl font-bold text-white mb-6">Apply for {job.title}</h2>
            
            <div className="space-y-4">
              {/* Cover Letter */}
              <div>
                <label className="block text-white mb-2">Cover Letter *</label>
                <textarea
                  value={applicationForm.coverLetter}
                  onChange={(e) => setApplicationForm(prev => ({ ...prev, coverLetter: e.target.value }))}
                  className="w-full bg-white/5 border border-white/10 rounded-lg p-3 text-white placeholder-gray-400 focus:border-galaxy-blue focus:outline-none"
                  rows={6}
                  placeholder="Tell us why you're interested in this position..."
                  required
                />
              </div>

              {/* Resume URL */}
              <div>
                <label className="block text-white mb-2">Resume URL *</label>
                <input
                  type="url"
                  value={applicationForm.resumeUrl}
                  onChange={(e) => setApplicationForm(prev => ({ ...prev, resumeUrl: e.target.value }))}
                  className="w-full bg-white/5 border border-white/10 rounded-lg p-3 text-white placeholder-gray-400 focus:border-galaxy-blue focus:outline-none"
                  placeholder="https://example.com/your-resume.pdf"
                  required
                />
              </div>

              {/* Portfolio URL */}
              <div>
                <label className="block text-white mb-2">Portfolio URL (Optional)</label>
                <input
                  type="url"
                  value={applicationForm.portfolioUrl}
                  onChange={(e) => setApplicationForm(prev => ({ ...prev, portfolioUrl: e.target.value }))}
                  className="w-full bg-white/5 border border-white/10 rounded-lg p-3 text-white placeholder-gray-400 focus:border-galaxy-blue focus:outline-none"
                  placeholder="https://example.com/your-portfolio"
                />
              </div>

              {/* Screening Questions */}
              {job.screeningQuestions && job.screeningQuestions.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Screening Questions</h3>
                  {job.screeningQuestions.map((question, index) => (
                    <div key={index} className="mb-4">
                      <label className="block text-white mb-2">
                        {question.question}
                        {question.required && <span className="text-red-400"> *</span>}
                      </label>
                      {question.type === 'text' ? (
                        <textarea
                          value={applicationForm.answers[index]?.answer || ''}
                          onChange={(e) => updateAnswer(index, e.target.value)}
                          className="w-full bg-white/5 border border-white/10 rounded-lg p-3 text-white placeholder-gray-400 focus:border-galaxy-blue focus:outline-none"
                          rows={3}
                          required={question.required}
                        />
                      ) : (
                        <select
                          value={applicationForm.answers[index]?.answer || ''}
                          onChange={(e) => updateAnswer(index, e.target.value)}
                          className="w-full bg-white/5 border border-white/10 rounded-lg p-3 text-white focus:border-galaxy-blue focus:outline-none"
                          required={question.required}
                        >
                          <option value="">Select an option</option>
                          {question.options?.map((option, optIndex) => (
                            <option key={optIndex} value={option}>{option}</option>
                          ))}
                        </select>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex space-x-4 mt-6">
              <button
                onClick={() => setShowApplicationForm(false)}
                className="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleApply}
                disabled={isApplying || !applicationForm.coverLetter || !applicationForm.resumeUrl}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isApplying ? 'Submitting...' : 'Submit Application'}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}
