'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  Briefcase,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  ExternalLink,
  Search,
  Filter,
  Download
} from 'lucide-react';
import { RoleGuard } from '@/components/auth/RoleGuard';
import toast from 'react-hot-toast';

interface JobApplication {
  _id: string;
  jobId: {
    _id: string;
    title: string;
    company: string;
    location: {
      city: string;
      country: string;
    };
  };
  status: 'applied' | 'under_review' | 'shortlisted' | 'interview_scheduled' | 'rejected' | 'hired';
  appliedAt: string;
  lastUpdated: string;
  coverLetter?: string;
  resumeUrl?: string;
  portfolioUrl?: string;
  interviewDetails?: {
    scheduledAt: string;
    type: string;
    location?: string;
    meetingLink?: string;
    notes?: string;
  };
  feedback?: {
    rating: number;
    comments: string;
  };
}

const statusConfig = {
  applied: {
    color: 'bg-blue-500',
    textColor: 'text-blue-400',
    bgColor: 'bg-blue-500/10',
    icon: Clock,
    label: 'Applied'
  },
  under_review: {
    color: 'bg-yellow-500',
    textColor: 'text-yellow-400',
    bgColor: 'bg-yellow-500/10',
    icon: AlertCircle,
    label: 'Under Review'
  },
  shortlisted: {
    color: 'bg-purple-500',
    textColor: 'text-purple-400',
    bgColor: 'bg-purple-500/10',
    icon: CheckCircle,
    label: 'Shortlisted'
  },
  interview_scheduled: {
    color: 'bg-indigo-500',
    textColor: 'text-indigo-400',
    bgColor: 'bg-indigo-500/10',
    icon: Calendar,
    label: 'Interview Scheduled'
  },
  rejected: {
    color: 'bg-red-500',
    textColor: 'text-red-400',
    bgColor: 'bg-red-500/10',
    icon: XCircle,
    label: 'Rejected'
  },
  hired: {
    color: 'bg-green-500',
    textColor: 'text-green-400',
    bgColor: 'bg-green-500/10',
    icon: CheckCircle,
    label: 'Hired'
  }
};

export default function JobApplicationsPage() {
  const { data: session } = useSession();
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async () => {
    try {
      const response = await fetch('/api/dashboard/student/applications');
      const result = await response.json();

      if (result.success) {
        setApplications(result.data);
      } else {
        toast.error('Failed to fetch applications');
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast.error('Failed to fetch applications');
    } finally {
      setIsLoading(false);
    }
  };

  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.jobId.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.jobId.company.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusStats = () => {
    const stats = {
      total: applications.length,
      applied: applications.filter(app => app.status === 'applied').length,
      under_review: applications.filter(app => app.status === 'under_review').length,
      shortlisted: applications.filter(app => app.status === 'shortlisted').length,
      interview_scheduled: applications.filter(app => app.status === 'interview_scheduled').length,
      rejected: applications.filter(app => app.status === 'rejected').length,
      hired: applications.filter(app => app.status === 'hired').length,
    };
    return stats;
  };

  const stats = getStatusStats();

  if (isLoading) {
    return (
      <div className="min-h-screen galaxy-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-galaxy-blue-light"></div>
      </div>
    );
  }

  return (
    <RoleGuard allowedRoles={['student']}>
      <div className="min-h-screen galaxy-background">
        <div className="star-field"></div>
        <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>
        
        <div className="relative z-10 section-container py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white galaxy-text-blue mb-2">
              My Job Applications
            </h1>
            <p className="text-gray-300">
              Track the status of your job applications and manage your career opportunities.
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="muted-glassmorphic rounded-lg p-4 glow-border"
            >
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{stats.total}</div>
                <div className="text-sm text-gray-300">Total</div>
              </div>
            </motion.div>

            {Object.entries(statusConfig).map(([status, config], index) => (
              <motion.div
                key={status}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="muted-glassmorphic rounded-lg p-4 glow-border"
              >
                <div className="text-center">
                  <div className={`text-2xl font-bold ${config.textColor}`}>
                    {stats[status as keyof typeof stats]}
                  </div>
                  <div className="text-sm text-gray-300">{config.label}</div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Filters */}
          <div className="muted-glassmorphic rounded-xl p-6 glow-border mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search by job title or company..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-white/5 border border-white/10 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-400 focus:border-galaxy-blue focus:outline-none"
                />
              </div>

              {/* Status Filter */}
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="bg-white/5 border border-white/10 rounded-lg pl-10 pr-8 py-3 text-white focus:border-galaxy-blue focus:outline-none appearance-none"
                >
                  <option value="all">All Status</option>
                  {Object.entries(statusConfig).map(([status, config]) => (
                    <option key={status} value={status}>{config.label}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Applications List */}
          <div className="space-y-4">
            {filteredApplications.length === 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="muted-glassmorphic rounded-xl p-8 glow-border text-center"
              >
                <Briefcase className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  {searchTerm || statusFilter !== 'all' ? 'No applications found' : 'No applications yet'}
                </h3>
                <p className="text-gray-300 mb-6">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Try adjusting your search or filter criteria.'
                    : 'Start applying for jobs to see your applications here.'
                  }
                </p>
                {!searchTerm && statusFilter === 'all' && (
                  <Link
                    href="/jobs"
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 glow-border"
                  >
                    <Briefcase className="h-5 w-5 mr-2" />
                    Browse Jobs
                  </Link>
                )}
              </motion.div>
            ) : (
              filteredApplications.map((application, index) => {
                const statusInfo = statusConfig[application.status];
                const StatusIcon = statusInfo.icon;

                return (
                  <motion.div
                    key={application._id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="muted-glassmorphic rounded-xl p-6 glow-border hover:bg-white/5 transition-all duration-300"
                  >
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                      {/* Job Info */}
                      <div className="flex-1 mb-4 lg:mb-0">
                        <div className="flex items-start space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold glow-border">
                            {application.jobId.company.split(' ').map(word => word[0]).join('')}
                          </div>
                          <div className="flex-1">
                            <h3 className="text-xl font-semibold text-white mb-1">
                              {application.jobId.title}
                            </h3>
                            <p className="text-gray-300 mb-2">
                              {application.jobId.company} • {application.jobId.location.city}, {application.jobId.location.country}
                            </p>
                            <div className="flex items-center space-x-4 text-sm text-gray-400">
                              <span>Applied {new Date(application.appliedAt).toLocaleDateString()}</span>
                              <span>Updated {new Date(application.lastUpdated).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Status and Actions */}
                      <div className="flex items-center space-x-4">
                        {/* Status Badge */}
                        <div className={`flex items-center space-x-2 px-3 py-2 rounded-full ${statusInfo.bgColor}`}>
                          <StatusIcon className={`h-4 w-4 ${statusInfo.textColor}`} />
                          <span className={`text-sm font-medium ${statusInfo.textColor}`}>
                            {statusInfo.label}
                          </span>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2">
                          <Link
                            href={`/jobs/${application.jobId._id}`}
                            className="p-2 bg-white/5 border border-white/10 rounded-lg hover:bg-white/10 transition-colors"
                            title="View Job"
                          >
                            <Eye className="h-4 w-4 text-gray-300" />
                          </Link>
                          {application.resumeUrl && (
                            <a
                              href={application.resumeUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="p-2 bg-white/5 border border-white/10 rounded-lg hover:bg-white/10 transition-colors"
                              title="View Resume"
                            >
                              <ExternalLink className="h-4 w-4 text-gray-300" />
                            </a>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Interview Details */}
                    {application.status === 'interview_scheduled' && application.interviewDetails && (
                      <div className="mt-4 p-4 bg-indigo-500/10 border border-indigo-500/20 rounded-lg">
                        <h4 className="text-indigo-400 font-semibold mb-2">Interview Scheduled</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-400">Date & Time:</span>
                            <span className="text-white ml-2">
                              {new Date(application.interviewDetails.scheduledAt).toLocaleString()}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-400">Type:</span>
                            <span className="text-white ml-2 capitalize">
                              {application.interviewDetails.type}
                            </span>
                          </div>
                          {application.interviewDetails.meetingLink && (
                            <div className="md:col-span-2">
                              <span className="text-gray-400">Meeting Link:</span>
                              <a
                                href={application.interviewDetails.meetingLink}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-galaxy-blue hover:text-galaxy-blue-light ml-2 underline"
                              >
                                Join Interview
                              </a>
                            </div>
                          )}
                          {application.interviewDetails.notes && (
                            <div className="md:col-span-2">
                              <span className="text-gray-400">Notes:</span>
                              <p className="text-white mt-1">{application.interviewDetails.notes}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Feedback */}
                    {application.feedback && (
                      <div className="mt-4 p-4 bg-white/5 border border-white/10 rounded-lg">
                        <h4 className="text-white font-semibold mb-2">Feedback</h4>
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-gray-400">Rating:</span>
                          <div className="flex space-x-1">
                            {[...Array(5)].map((_, i) => (
                              <div
                                key={i}
                                className={`w-4 h-4 rounded-full ${
                                  i < application.feedback!.rating ? 'bg-yellow-400' : 'bg-gray-600'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-gray-300">{application.feedback.comments}</p>
                      </div>
                    )}
                  </motion.div>
                );
              })
            )}
          </div>
        </div>
      </div>
    </RoleGuard>
  );
}
