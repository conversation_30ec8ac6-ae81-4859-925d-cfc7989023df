'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import {
  Play,
  Pause,
  SkipBack,
  SkipFor<PERSON>,
  CheckCircle,
  Clock,
  BookOpen,
  FileText,
  HelpCircle,
  ArrowLeft,
  Menu,
  X
} from 'lucide-react';
import { RoleGuard } from '@/components/auth/RoleGuard';
import toast from 'react-hot-toast';

interface Course {
  _id: string;
  title: string;
  modules: {
    title: string;
    description: string;
    order: number;
    lessons: {
      title: string;
      type: 'video' | 'text' | 'quiz' | 'assignment';
      content: string;
      duration?: number;
      order: number;
    }[];
  }[];
}

interface Enrollment {
  _id: string;
  progress: {
    completedLessons: string[];
    currentModule: number;
    currentLesson: number;
    timeSpent: number;
  };
}

export default function CourseLearnPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  
  const [course, setCourse] = useState<Course | null>(null);
  const [enrollment, setEnrollment] = useState<Enrollment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentModuleIndex, setCurrentModuleIndex] = useState(0);
  const [currentLessonIndex, setCurrentLessonIndex] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [lessonStartTime, setLessonStartTime] = useState<Date | null>(null);

  useEffect(() => {
    fetchCourseData();
  }, [params.id]);

  useEffect(() => {
    // Get module and lesson from URL params
    const moduleParam = searchParams.get('module');
    const lessonParam = searchParams.get('lesson');
    
    if (moduleParam !== null) {
      setCurrentModuleIndex(parseInt(moduleParam));
    }
    if (lessonParam !== null) {
      setCurrentLessonIndex(parseInt(lessonParam));
    }
  }, [searchParams]);

  useEffect(() => {
    // Track lesson start time
    setLessonStartTime(new Date());
  }, [currentModuleIndex, currentLessonIndex]);

  const fetchCourseData = async () => {
    try {
      const response = await fetch(`/api/courses/${params.id}`);
      const result = await response.json();

      if (result.success) {
        setCourse(result.data.course);
        setEnrollment(result.data.enrollment);
        
        if (!result.data.isEnrolled) {
          toast.error('You must be enrolled to access course content');
          router.push(`/courses/${params.id}`);
          return;
        }

        // Set current position from enrollment
        if (result.data.enrollment?.progress) {
          setCurrentModuleIndex(result.data.enrollment.progress.currentModule || 0);
          setCurrentLessonIndex(result.data.enrollment.progress.currentLesson || 0);
        }
      } else {
        toast.error('Course not found');
        router.push('/courses');
      }
    } catch (error) {
      console.error('Error fetching course:', error);
      toast.error('Failed to load course');
    } finally {
      setIsLoading(false);
    }
  };

  const markLessonComplete = async () => {
    if (!course || !enrollment) return;

    const lessonId = `${currentModuleIndex}-${currentLessonIndex}`;
    
    // Calculate time spent on this lesson
    const timeSpent = lessonStartTime ? Math.round((new Date().getTime() - lessonStartTime.getTime()) / 1000 / 60) : 0;

    try {
      const response = await fetch(`/api/enrollments/${enrollment._id}/progress`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lessonId,
          timeSpent,
          moduleIndex: currentModuleIndex,
          lessonIndex: currentLessonIndex,
        }),
      });

      if (response.ok) {
        // Update local state
        setEnrollment(prev => prev ? {
          ...prev,
          progress: {
            ...prev.progress,
            completedLessons: [...prev.progress.completedLessons, lessonId],
            timeSpent: prev.progress.timeSpent + timeSpent,
          }
        } : null);
        
        toast.success('Lesson completed!');
      }
    } catch (error) {
      console.error('Error marking lesson complete:', error);
    }
  };

  const navigateToLesson = (moduleIndex: number, lessonIndex: number) => {
    setCurrentModuleIndex(moduleIndex);
    setCurrentLessonIndex(lessonIndex);
    setSidebarOpen(false);
    
    // Update URL
    const url = new URL(window.location.href);
    url.searchParams.set('module', moduleIndex.toString());
    url.searchParams.set('lesson', lessonIndex.toString());
    window.history.pushState({}, '', url.toString());
  };

  const goToNextLesson = () => {
    if (!course) return;

    const currentModule = course.modules[currentModuleIndex];
    if (currentLessonIndex < currentModule.lessons.length - 1) {
      // Next lesson in same module
      navigateToLesson(currentModuleIndex, currentLessonIndex + 1);
    } else if (currentModuleIndex < course.modules.length - 1) {
      // First lesson of next module
      navigateToLesson(currentModuleIndex + 1, 0);
    }
  };

  const goToPreviousLesson = () => {
    if (!course) return;

    if (currentLessonIndex > 0) {
      // Previous lesson in same module
      navigateToLesson(currentModuleIndex, currentLessonIndex - 1);
    } else if (currentModuleIndex > 0) {
      // Last lesson of previous module
      const prevModule = course.modules[currentModuleIndex - 1];
      navigateToLesson(currentModuleIndex - 1, prevModule.lessons.length - 1);
    }
  };

  const isLessonCompleted = (moduleIndex: number, lessonIndex: number) => {
    const lessonId = `${moduleIndex}-${lessonIndex}`;
    return enrollment?.progress.completedLessons.includes(lessonId) || false;
  };

  const getCurrentLesson = () => {
    if (!course || !course.modules[currentModuleIndex]) return null;
    return course.modules[currentModuleIndex].lessons[currentLessonIndex];
  };

  const renderLessonContent = () => {
    const lesson = getCurrentLesson();
    if (!lesson) return null;

    switch (lesson.type) {
      case 'video':
        return (
          <div className="aspect-video bg-black rounded-lg overflow-hidden">
            <video
              src={lesson.content}
              controls
              className="w-full h-full"
              onEnded={markLessonComplete}
            >
              Your browser does not support the video tag.
            </video>
          </div>
        );

      case 'text':
        return (
          <div className="prose prose-invert max-w-none">
            <div className="bg-white/5 rounded-lg p-6">
              <div className="whitespace-pre-wrap text-gray-300">
                {lesson.content}
              </div>
              <div className="mt-6 pt-6 border-t border-white/10">
                <button
                  onClick={markLessonComplete}
                  className="bg-galaxy-blue text-white px-6 py-2 rounded-lg hover:bg-galaxy-blue-light transition-colors"
                >
                  Mark as Complete
                </button>
              </div>
            </div>
          </div>
        );

      case 'quiz':
        return (
          <div className="bg-white/5 rounded-lg p-6">
            <div className="text-center">
              <HelpCircle className="h-16 w-16 text-galaxy-blue mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Quiz</h3>
              <p className="text-gray-300 mb-6">
                Interactive quiz content would be implemented here
              </p>
              <button
                onClick={markLessonComplete}
                className="bg-galaxy-blue text-white px-6 py-2 rounded-lg hover:bg-galaxy-blue-light transition-colors"
              >
                Start Quiz
              </button>
            </div>
          </div>
        );

      case 'assignment':
        return (
          <div className="bg-white/5 rounded-lg p-6">
            <div className="text-center">
              <FileText className="h-16 w-16 text-galaxy-blue mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Assignment</h3>
              <p className="text-gray-300 mb-6">
                Assignment submission interface would be implemented here
              </p>
              <button
                onClick={markLessonComplete}
                className="bg-galaxy-blue text-white px-6 py-2 rounded-lg hover:bg-galaxy-blue-light transition-colors"
              >
                Submit Assignment
              </button>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center text-gray-400">
            Unsupported lesson type: {lesson.type}
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen galaxy-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-galaxy-blue-light"></div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="min-h-screen galaxy-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Course Not Found</h1>
          <button
            onClick={() => router.push('/courses')}
            className="bg-galaxy-blue text-white px-6 py-2 rounded-lg hover:bg-galaxy-blue-light transition-colors"
          >
            Back to Courses
          </button>
        </div>
      </div>
    );
  }

  const currentLesson = getCurrentLesson();
  const currentModule = course.modules[currentModuleIndex];

  return (
    <RoleGuard allowedRoles={['student']}>
      <div className="min-h-screen galaxy-background">
        <div className="star-field"></div>
        <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>
        
        <div className="relative z-10 flex h-screen">
          {/* Sidebar */}
          <div className={`fixed inset-y-0 left-0 z-50 w-80 bg-galaxy-deep border-r border-white/10 transform transition-transform duration-300 ease-in-out ${
            sidebarOpen ? 'translate-x-0' : '-translate-x-full'
          } lg:translate-x-0 lg:static lg:inset-0`}>
            <div className="flex items-center justify-between p-4 border-b border-white/10">
              <h2 className="text-lg font-semibold text-white truncate">{course.title}</h2>
              <button
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden text-gray-400 hover:text-white"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <div className="overflow-y-auto h-full pb-20">
              {course.modules.map((module, moduleIndex) => (
                <div key={moduleIndex} className="border-b border-white/10">
                  <div className="p-4 bg-white/5">
                    <h3 className="font-semibold text-white text-sm">
                      Module {module.order}: {module.title}
                    </h3>
                  </div>
                  <div className="divide-y divide-white/10">
                    {module.lessons.map((lesson, lessonIndex) => {
                      const isCompleted = isLessonCompleted(moduleIndex, lessonIndex);
                      const isCurrent = moduleIndex === currentModuleIndex && lessonIndex === currentLessonIndex;
                      
                      return (
                        <button
                          key={lessonIndex}
                          onClick={() => navigateToLesson(moduleIndex, lessonIndex)}
                          className={`w-full p-3 text-left hover:bg-white/5 transition-colors ${
                            isCurrent ? 'bg-galaxy-blue/20 border-r-2 border-galaxy-blue' : ''
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            {isCompleted ? (
                              <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                            ) : (
                              <div className="w-4 h-4 border border-gray-400 rounded-full flex-shrink-0" />
                            )}
                            <div className="flex-1 min-w-0">
                              <div className="text-white text-sm truncate">{lesson.title}</div>
                              <div className="flex items-center space-x-2 text-xs text-gray-400">
                                <span className="capitalize">{lesson.type}</span>
                                {lesson.duration && (
                                  <>
                                    <span>•</span>
                                    <span>{lesson.duration} min</span>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="bg-galaxy-deep border-b border-white/10 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setSidebarOpen(true)}
                    className="lg:hidden text-gray-400 hover:text-white"
                  >
                    <Menu className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => router.push(`/courses/${course._id}`)}
                    className="flex items-center space-x-2 text-galaxy-blue hover:text-galaxy-blue-light transition-colors"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    <span className="hidden sm:inline">Back to Course</span>
                  </button>
                </div>
                
                <div className="flex items-center space-x-4">
                  <button
                    onClick={goToPreviousLesson}
                    disabled={currentModuleIndex === 0 && currentLessonIndex === 0}
                    className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <SkipBack className="h-5 w-5" />
                  </button>
                  <button
                    onClick={goToNextLesson}
                    disabled={
                      currentModuleIndex === course.modules.length - 1 &&
                      currentLessonIndex === course.modules[currentModuleIndex].lessons.length - 1
                    }
                    className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <SkipForward className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Lesson Content */}
            <div className="flex-1 overflow-y-auto">
              <div className="max-w-4xl mx-auto p-6">
                {/* Lesson Header */}
                <div className="mb-6">
                  <div className="flex items-center space-x-2 text-sm text-gray-400 mb-2">
                    <span>Module {currentModule.order}</span>
                    <span>•</span>
                    <span>Lesson {currentLessonIndex + 1}</span>
                    {currentLesson?.duration && (
                      <>
                        <span>•</span>
                        <Clock className="h-4 w-4" />
                        <span>{currentLesson.duration} min</span>
                      </>
                    )}
                  </div>
                  <h1 className="text-2xl font-bold text-white mb-2">
                    {currentLesson?.title}
                  </h1>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs bg-galaxy-blue/20 text-galaxy-blue px-2 py-1 rounded-full capitalize">
                      {currentLesson?.type}
                    </span>
                    {isLessonCompleted(currentModuleIndex, currentLessonIndex) && (
                      <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full flex items-center space-x-1">
                        <CheckCircle className="h-3 w-3" />
                        <span>Completed</span>
                      </span>
                    )}
                  </div>
                </div>

                {/* Lesson Content */}
                <div className="mb-8">
                  {renderLessonContent()}
                </div>

                {/* Navigation */}
                <div className="flex items-center justify-between pt-6 border-t border-white/10">
                  <button
                    onClick={goToPreviousLesson}
                    disabled={currentModuleIndex === 0 && currentLessonIndex === 0}
                    className="flex items-center space-x-2 px-4 py-2 bg-white/5 border border-white/10 rounded-lg hover:bg-white/10 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <SkipBack className="h-4 w-4" />
                    <span>Previous</span>
                  </button>
                  
                  <button
                    onClick={goToNextLesson}
                    disabled={
                      currentModuleIndex === course.modules.length - 1 &&
                      currentLessonIndex === course.modules[currentModuleIndex].lessons.length - 1
                    }
                    className="flex items-center space-x-2 px-4 py-2 bg-galaxy-blue text-white rounded-lg hover:bg-galaxy-blue-light transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span>Next</span>
                    <SkipForward className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Sidebar Overlay */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </div>
    </RoleGuard>
  );
}
