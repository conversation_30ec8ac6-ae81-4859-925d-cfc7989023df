'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Star,
  Share2,
  ArrowLeft,
  Video,
  CheckCircle,
  AlertCircle,
  DollarSign,
  Globe,
  User,
  Building
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Event {
  _id: string;
  title: string;
  description: string;
  type: 'webinar' | 'workshop' | 'conference' | 'job_fair' | 'networking' | 'competition' | 'meetup';
  category: string;
  organizer: {
    name: string;
    organization?: string;
    email: string;
    userId?: string;
  };
  speakers: {
    name: string;
    title: string;
    organization?: string;
    bio?: string;
    avatar?: string;
  }[];
  schedule: {
    startDate: string;
    endDate: string;
    timezone: string;
    sessions?: {
      title: string;
      description?: string;
      startTime: string;
      endTime: string;
      speaker?: string;
    }[];
  };
  location: {
    type: 'online' | 'in_person' | 'hybrid';
    venue?: string;
    address?: string;
    city?: string;
    country?: string;
    meetingLink?: string;
    platform?: string;
  };
  registration: {
    isRequired: boolean;
    maxAttendees?: number;
    currentAttendees: number;
    deadline?: string;
    fee: number;
    currency: string;
    isFree: boolean;
    requiresApproval: boolean;
  };
  content?: {
    agenda?: string;
    requirements?: string[];
    materials?: string[];
  };
  tags: string[];
  thumbnail?: string;
  status: 'draft' | 'published' | 'cancelled' | 'completed';
  visibility: 'public' | 'private' | 'members_only';
  featured: boolean;
  analytics: {
    views: number;
    registrations: number;
    attendanceRate: number;
    averageRating: number;
    totalRatings: number;
  };
  createdAt: string;
}

interface Registration {
  _id: string;
  status: 'registered' | 'confirmed' | 'attended' | 'no_show' | 'cancelled';
  registeredAt: string;
}

export default function EventDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [event, setEvent] = useState<Event | null>(null);
  const [registration, setRegistration] = useState<Registration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRegistering, setIsRegistering] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);
  const [showRegistrationForm, setShowRegistrationForm] = useState(false);
  const [specialRequests, setSpecialRequests] = useState('');

  useEffect(() => {
    fetchEventDetails();
  }, [params.id]);

  const fetchEventDetails = async () => {
    try {
      const response = await fetch(`/api/events/${params.id}`);
      const result = await response.json();

      if (result.success) {
        setEvent(result.data.event);
        setRegistration(result.data.registration);
        setIsRegistered(result.data.isRegistered);
      } else {
        toast.error('Event not found');
        router.push('/events');
      }
    } catch (error) {
      console.error('Error fetching event:', error);
      toast.error('Failed to load event details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async () => {
    if (!session) {
      toast.error('Please sign in to register for events');
      router.push('/auth/signin');
      return;
    }

    setIsRegistering(true);
    try {
      const response = await fetch(`/api/events/${params.id}/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ specialRequests }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Successfully registered for event!');
        setIsRegistered(true);
        setShowRegistrationForm(false);
        // Refresh event data to get updated attendee count
        fetchEventDetails();
      } else {
        toast.error(result.error || 'Failed to register for event');
      }
    } catch (error) {
      console.error('Error registering for event:', error);
      toast.error('Failed to register for event');
    } finally {
      setIsRegistering(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const isEventFull = () => {
    return event?.registration.maxAttendees && 
           event.registration.currentAttendees >= event.registration.maxAttendees;
  };

  const isRegistrationOpen = () => {
    if (!event) return false;
    if (event.registration.deadline) {
      return new Date() < new Date(event.registration.deadline);
    }
    return new Date() < new Date(event.schedule.startDate);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen galaxy-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-galaxy-blue-light"></div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="min-h-screen galaxy-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Event Not Found</h1>
          <button
            onClick={() => router.push('/events')}
            className="bg-galaxy-blue text-white px-6 py-2 rounded-lg hover:bg-galaxy-blue-light transition-colors"
          >
            Back to Events
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen galaxy-background">
      <div className="star-field"></div>
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>
      
      <div className="relative z-10 section-container py-8">
        {/* Back Button */}
        <button
          onClick={() => router.back()}
          className="flex items-center space-x-2 text-galaxy-blue hover:text-galaxy-blue-light transition-colors mb-6"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Back to Events</span>
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Event Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              {/* Event Image/Thumbnail */}
              <div className="relative h-64 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg mb-6 overflow-hidden">
                {event.thumbnail ? (
                  <img
                    src={event.thumbnail}
                    alt={event.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Calendar className="h-20 w-20 text-white opacity-80" />
                  </div>
                )}
                {event.featured && (
                  <div className="absolute top-4 left-4 bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-semibold">
                    Featured
                  </div>
                )}
                <div className="absolute top-4 right-4 flex space-x-2">
                  <button className="p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors">
                    <Share2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="flex items-center space-x-2 mb-4">
                <span className="text-xs font-medium galaxy-text-blue bg-blue-600/20 px-3 py-1 rounded-full capitalize">
                  {event.type}
                </span>
                <span className="text-xs font-medium text-gray-300 bg-white/10 px-3 py-1 rounded-full">
                  {event.category}
                </span>
                {event.location.type === 'online' && (
                  <span className="text-xs font-medium text-green-400 bg-green-600/20 px-3 py-1 rounded-full">
                    Virtual
                  </span>
                )}
              </div>

              <h1 className="text-3xl font-bold text-white font-orbitron mb-4">
                {event.title}
              </h1>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="flex items-center space-x-3 text-gray-300">
                  <Calendar className="h-5 w-5 text-galaxy-blue" />
                  <div>
                    <p className="font-medium">{formatDate(event.schedule.startDate)}</p>
                    <p className="text-sm text-gray-400">
                      {formatTime(event.schedule.startDate)} - {formatTime(event.schedule.endDate)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 text-gray-300">
                  {event.location.type === 'online' ? (
                    <Video className="h-5 w-5 text-galaxy-purple" />
                  ) : (
                    <MapPin className="h-5 w-5 text-galaxy-purple" />
                  )}
                  <div>
                    <p className="font-medium">
                      {event.location.type === 'online' 
                        ? `Online (${event.location.platform || 'Virtual'})`
                        : event.location.venue || 'In-person'
                      }
                    </p>
                    {event.location.city && (
                      <p className="text-sm text-gray-400">
                        {event.location.city}, {event.location.country}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3 text-gray-300">
                  <Users className="h-5 w-5 text-galaxy-pink" />
                  <div>
                    <p className="font-medium">
                      {event.registration.currentAttendees} registered
                    </p>
                    {event.registration.maxAttendees && (
                      <p className="text-sm text-gray-400">
                        of {event.registration.maxAttendees} max
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3 text-gray-300">
                  <Building className="h-5 w-5 text-galaxy-blue" />
                  <div>
                    <p className="font-medium">{event.organizer.name}</p>
                    {event.organizer.organization && (
                      <p className="text-sm text-gray-400">{event.organizer.organization}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Event Description */}
              <div className="prose prose-invert max-w-none">
                <p className="text-gray-300 whitespace-pre-wrap">
                  {event.description}
                </p>
              </div>
            </motion.div>

            {/* Speakers */}
            {event.speakers.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <h2 className="text-xl font-semibold text-white mb-6">Speakers</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {event.speakers.map((speaker, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                        {speaker.avatar ? (
                          <img
                            src={speaker.avatar}
                            alt={speaker.name}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          speaker.name.split(' ').map(n => n[0]).join('')
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-white">{speaker.name}</h3>
                        <p className="text-galaxy-blue text-sm">{speaker.title}</p>
                        {speaker.organization && (
                          <p className="text-gray-400 text-sm">{speaker.organization}</p>
                        )}
                        {speaker.bio && (
                          <p className="text-gray-300 text-sm mt-2">{speaker.bio}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Agenda/Sessions */}
            {event.schedule.sessions && event.schedule.sessions.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <h2 className="text-xl font-semibold text-white mb-6">Agenda</h2>
                <div className="space-y-4">
                  {event.schedule.sessions.map((session, index) => (
                    <div key={index} className="border-l-2 border-galaxy-blue pl-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Clock className="h-4 w-4 text-galaxy-blue" />
                        <span className="text-sm text-galaxy-blue font-medium">
                          {formatTime(session.startTime)} - {formatTime(session.endTime)}
                        </span>
                      </div>
                      <h3 className="font-semibold text-white mb-1">{session.title}</h3>
                      {session.description && (
                        <p className="text-gray-300 text-sm mb-2">{session.description}</p>
                      )}
                      {session.speaker && (
                        <p className="text-galaxy-purple text-sm">Speaker: {session.speaker}</p>
                      )}
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Requirements & Materials */}
            {event.content && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <h2 className="text-xl font-semibold text-white mb-6">Event Details</h2>
                
                {event.content.requirements && event.content.requirements.length > 0 && (
                  <div className="mb-6">
                    <h3 className="font-semibold text-galaxy-blue mb-3">Requirements</h3>
                    <ul className="space-y-2">
                      {event.content.requirements.map((req, index) => (
                        <li key={index} className="flex items-start space-x-2 text-gray-300">
                          <CheckCircle className="h-4 w-4 text-galaxy-blue mt-1 flex-shrink-0" />
                          <span>{req}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {event.content.materials && event.content.materials.length > 0 && (
                  <div>
                    <h3 className="font-semibold text-galaxy-blue mb-3">Materials Provided</h3>
                    <ul className="space-y-2">
                      {event.content.materials.map((material, index) => (
                        <li key={index} className="flex items-start space-x-2 text-gray-300">
                          <CheckCircle className="h-4 w-4 text-galaxy-blue mt-1 flex-shrink-0" />
                          <span>{material}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </motion.div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Registration Card */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              {isRegistered ? (
                <div className="text-center">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">You're Registered!</h3>
                  <p className="text-gray-300 text-sm mb-4">
                    {registration?.status === 'confirmed' 
                      ? "Your registration is confirmed. You'll receive event details soon."
                      : "Your registration is pending approval."
                    }
                  </p>
                  {event.location.type === 'online' && event.location.meetingLink && (
                    <a
                      href={event.location.meetingLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-purple-600 transition-all duration-300 glow-border inline-block"
                    >
                      Join Event
                    </a>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">
                      {event.registration.isFree ? 'Free' : `${event.registration.currency} ${event.registration.fee}`}
                    </div>
                    {!event.registration.isFree && (
                      <p className="text-gray-300 text-sm">Registration fee</p>
                    )}
                  </div>

                  {/* Registration Status */}
                  {!isRegistrationOpen() ? (
                    <div className="text-center p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
                      <AlertCircle className="h-8 w-8 text-red-400 mx-auto mb-2" />
                      <p className="text-red-400 font-semibold">Registration Closed</p>
                    </div>
                  ) : isEventFull() ? (
                    <div className="text-center p-4 bg-yellow-500/20 border border-yellow-500/30 rounded-lg">
                      <AlertCircle className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                      <p className="text-yellow-400 font-semibold">Event Full</p>
                    </div>
                  ) : (
                    <button
                      onClick={() => setShowRegistrationForm(true)}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 glow-border"
                    >
                      Register Now
                    </button>
                  )}

                  {event.registration.deadline && (
                    <p className="text-center text-sm text-gray-400">
                      Registration deadline: {formatDate(event.registration.deadline)}
                    </p>
                  )}
                </div>
              )}
            </motion.div>

            {/* Event Info */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h3 className="text-lg font-semibold text-white mb-4">Event Information</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Duration</span>
                  <span className="text-white">
                    {Math.ceil((new Date(event.schedule.endDate).getTime() - new Date(event.schedule.startDate).getTime()) / (1000 * 60 * 60))} hours
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Format</span>
                  <span className="text-white capitalize">{event.location.type.replace('_', ' ')}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Language</span>
                  <span className="text-white">English</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Timezone</span>
                  <span className="text-white">{event.schedule.timezone}</span>
                </div>
                {event.analytics.averageRating > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Rating</span>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-white">{event.analytics.averageRating.toFixed(1)}</span>
                      <span className="text-gray-400">({event.analytics.totalRatings})</span>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>

            {/* Tags */}
            {event.tags.length > 0 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <h3 className="text-lg font-semibold text-white mb-4">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {event.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-galaxy-blue/20 text-galaxy-blue px-3 py-1 rounded-full text-sm border border-galaxy-blue/30"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Registration Form Modal */}
      {showRegistrationForm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-galaxy-deep border border-white/10 rounded-xl p-6 max-w-md w-full"
          >
            <h2 className="text-2xl font-bold text-white mb-6">Register for {event.title}</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-white mb-2">Special Requests (Optional)</label>
                <textarea
                  value={specialRequests}
                  onChange={(e) => setSpecialRequests(e.target.value)}
                  className="w-full bg-white/5 border border-white/10 rounded-lg p-3 text-white placeholder-gray-400 focus:border-galaxy-blue focus:outline-none"
                  rows={4}
                  placeholder="Any special requirements or dietary restrictions..."
                />
              </div>

              {event.registration.requiresApproval && (
                <div className="p-4 bg-yellow-500/20 border border-yellow-500/30 rounded-lg">
                  <p className="text-yellow-400 text-sm">
                    This event requires approval. You'll be notified once your registration is reviewed.
                  </p>
                </div>
              )}
            </div>

            <div className="flex space-x-4 mt-6">
              <button
                onClick={() => setShowRegistrationForm(false)}
                className="flex-1 bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleRegister}
                disabled={isRegistering}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isRegistering ? 'Registering...' : 'Confirm Registration'}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}
