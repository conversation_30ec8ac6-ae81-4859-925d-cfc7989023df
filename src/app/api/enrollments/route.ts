import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import Enrollment from '@/models/Enrollment';
import Course from '@/models/Course';
import { User } from '@/models/User';
import { SMSService } from '@/lib/sms';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status'); // 'active', 'completed'

    // Build query
    const query: any = { userId: session.user.id, isActive: true };

    if (status === 'completed') {
      query.completedAt = { $exists: true };
    } else if (status === 'active') {
      query.completedAt = { $exists: false };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get enrollments with course details
    const enrollments = await Enrollment.find(query)
      .populate('courseId', 'title description instructor thumbnail category level duration')
      .sort({ enrolledAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await Enrollment.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: {
        enrollments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching enrollments:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch enrollments' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { courseId } = await request.json();

    if (!courseId) {
      return NextResponse.json(
        { success: false, error: 'Course ID is required' },
        { status: 400 }
      );
    }

    // Check if course exists and is published
    const course = await Course.findById(courseId);
    if (!course || !course.isPublished) {
      return NextResponse.json(
        { success: false, error: 'Course not found or not available' },
        { status: 404 }
      );
    }

    // Check if user is already enrolled
    const existingEnrollment = await Enrollment.findOne({
      userId: session.user.id,
      courseId: courseId,
    });

    if (existingEnrollment) {
      return NextResponse.json(
        { success: false, error: 'Already enrolled in this course' },
        { status: 400 }
      );
    }

    // Create enrollment
    const enrollment = new Enrollment({
      userId: session.user.id,
      courseId: courseId,
      progress: {
        totalModules: course.modules.length,
      },
    });

    await enrollment.save();

    // Update course enrollment count
    await Course.findByIdAndUpdate(courseId, {
      $inc: { 'enrollment.totalNerds': 1 },
    });

    // Send SMS notification to the enrolled user
    try {
      const user = await User.findById(session.user.id);
      if (user?.phone) {
        await SMSService.sendCourseEnrollmentConfirmation(
          user.phone,
          user.name,
          course.title
        );
      }
    } catch (smsError) {
      console.error('Error sending course enrollment SMS notification:', smsError);
      // Don't fail the enrollment if SMS fails
    }

    return NextResponse.json({
      success: true,
      data: enrollment,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating enrollment:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to enroll in course' },
      { status: 500 }
    );
  }
}
