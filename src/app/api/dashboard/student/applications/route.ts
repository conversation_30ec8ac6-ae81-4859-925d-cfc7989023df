import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { JobApplication } from '@/models/JobApplication';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request: NextRequest) {
  return requireAuth(request, async (req) => {
    try {
      await connectDB();

      const userId = req.user?.id;
      const { searchParams } = new URL(request.url);
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '10');
      const status = searchParams.get('status');
      const skip = (page - 1) * limit;

      // Build filter
      const filter: any = { applicantId: userId };
      if (status && status !== 'all') {
        filter.status = status;
      }

      // Get applications with job details
      const applications = await JobApplication.find(filter)
        .populate({
          path: 'jobId',
          select: 'title company location status'
        })
        .sort({ appliedAt: -1 })
        .limit(limit)
        .skip(skip)
        .lean();

      // Get total count for pagination
      const totalApplications = await JobApplication.countDocuments(filter);

      // Transform the data to match frontend expectations
      const transformedApplications = applications.map(app => ({
        _id: app._id.toString(),
        jobId: {
          _id: app.jobId?._id?.toString() || '',
          title: app.jobId?.title || 'Unknown Job',
          company: app.jobId?.company || 'Unknown Company',
          location: app.jobId?.location || { city: 'Unknown', country: 'Unknown' }
        },
        status: app.status,
        appliedAt: app.appliedAt,
        lastUpdated: app.lastUpdated,
        coverLetter: app.coverLetter,
        resumeUrl: app.resumeUrl,
        portfolioUrl: app.portfolioUrl,
        interviewDetails: app.interviewDetails,
        feedback: app.feedback,
        notes: app.notes
      }));

      return NextResponse.json({
        success: true,
        data: transformedApplications,
        pagination: {
          page,
          limit,
          total: totalApplications,
          pages: Math.ceil(totalApplications / limit)
        }
      });
    } catch (error) {
      console.error('Error fetching student applications:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch applications' },
        { status: 500 }
      );
    }
  });
}

// Update application status (for withdrawing applications)
export async function PATCH(request: NextRequest) {
  return requireAuth(request, async (req) => {
    try {
      await connectDB();

      const userId = req.user?.id;
      const body = await request.json();
      const { applicationId, action } = body;

      if (!applicationId || !action) {
        return NextResponse.json(
          { success: false, error: 'Application ID and action are required' },
          { status: 400 }
        );
      }

      // Find the application
      const application = await JobApplication.findOne({
        _id: applicationId,
        applicantId: userId
      });

      if (!application) {
        return NextResponse.json(
          { success: false, error: 'Application not found' },
          { status: 404 }
        );
      }

      // Handle different actions
      switch (action) {
        case 'withdraw':
          // Only allow withdrawal if application is in early stages
          if (['applied', 'under_review'].includes(application.status)) {
            application.status = 'withdrawn' as any; // Add withdrawn to enum if needed
            application.lastUpdated = new Date();
            await application.save();
            
            return NextResponse.json({
              success: true,
              message: 'Application withdrawn successfully'
            });
          } else {
            return NextResponse.json(
              { success: false, error: 'Cannot withdraw application at this stage' },
              { status: 400 }
            );
          }

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid action' },
            { status: 400 }
          );
      }
    } catch (error) {
      console.error('Error updating application:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update application' },
        { status: 500 }
      );
    }
  });
}
