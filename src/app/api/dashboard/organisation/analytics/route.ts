import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { Job } from '@/models/Job';
import { JobApplication } from '@/models/JobApplication';
import { Course } from '@/models/Course';
import { Enrollment } from '@/models/Enrollment';
import { Certificate } from '@/models/Certificate';
import { requireAuth } from '@/lib/auth-middleware';

export async function GET(request: NextRequest) {
  return requireAuth(request, async (req) => {
    // Check if user is an organisation
    if (req.user?.role !== 'organisation' && req.user?.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Organisation role required.' },
        { status: 403 }
      );
    }

    try {
      await connectDB();

      const userId = req.user?.id;
      const { searchParams } = new URL(request.url);
      const days = parseInt(searchParams.get('days') || '30');
      
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // Get organization's jobs and courses
      const [orgJobs, orgCourses] = await Promise.all([
        Job.find({ employerId: userId }).select('_id'),
        Course.find({ instructorId: userId }).select('_id')
      ]);

      const jobIds = orgJobs.map(job => job._id);
      const courseIds = orgCourses.map(course => course._id);

      // Overview statistics
      const [
        totalJobs,
        totalApplications,
        totalCourses,
        totalStudents,
        totalCertificates,
        recentApplications,
        totalJobViews
      ] = await Promise.all([
        Job.countDocuments({ employerId: userId, isActive: true }),
        JobApplication.countDocuments({ jobId: { $in: jobIds } }),
        Course.countDocuments({ instructorId: userId, isActive: true }),
        Enrollment.countDocuments({ courseId: { $in: courseIds }, isActive: true }),
        Certificate.countDocuments({ courseId: { $in: courseIds } }),
        JobApplication.countDocuments({
          jobId: { $in: jobIds },
          appliedAt: { $gte: startDate }
        }),
        // Placeholder for job views - would need analytics tracking
        Promise.resolve(0)
      ]);

      // Calculate application rate (placeholder calculation)
      const applicationRate = totalJobViews > 0 ? (totalApplications / totalJobViews) * 100 : 0;

      // Calculate average time to hire
      const hiredApplications = await JobApplication.find({
        jobId: { $in: jobIds },
        status: 'hired',
        appliedAt: { $exists: true },
        lastUpdated: { $exists: true }
      }).select('appliedAt lastUpdated');

      const avgTimeToHire = hiredApplications.length > 0
        ? hiredApplications.reduce((sum, app) => {
            const timeDiff = new Date(app.lastUpdated).getTime() - new Date(app.appliedAt).getTime();
            return sum + (timeDiff / (1000 * 60 * 60 * 24)); // Convert to days
          }, 0) / hiredApplications.length
        : 0;

      // Calculate success rate
      const successRate = totalApplications > 0 
        ? (hiredApplications.length / totalApplications) * 100 
        : 0;

      // Job performance data
      const jobPerformance = await JobApplication.aggregate([
        { $match: { jobId: { $in: jobIds } } },
        {
          $group: {
            _id: '$jobId',
            applications: { $sum: 1 },
            views: { $sum: 1 } // Placeholder - would need actual view tracking
          }
        },
        {
          $lookup: {
            from: 'jobs',
            localField: '_id',
            foreignField: '_id',
            as: 'job'
          }
        },
        { $unwind: '$job' },
        {
          $project: {
            jobId: '$_id',
            title: '$job.title',
            applications: 1,
            views: 1,
            conversionRate: {
              $cond: {
                if: { $gt: ['$views', 0] },
                then: { $multiply: [{ $divide: ['$applications', '$views'] }, 100] },
                else: 0
              }
            },
            status: '$job.status'
          }
        },
        { $sort: { applications: -1 } },
        { $limit: 10 }
      ]);

      // Course popularity data
      const coursePopularity = await Enrollment.aggregate([
        { $match: { courseId: { $in: courseIds }, isActive: true } },
        {
          $group: {
            _id: '$courseId',
            enrollments: { $sum: 1 },
            completions: {
              $sum: {
                $cond: [{ $ne: ['$completedAt', null] }, 1, 0]
              }
            }
          }
        },
        {
          $lookup: {
            from: 'courses',
            localField: '_id',
            foreignField: '_id',
            as: 'course'
          }
        },
        { $unwind: '$course' },
        {
          $project: {
            courseId: '$_id',
            title: '$course.title',
            enrollments: 1,
            completions: 1,
            rating: '$course.ratings.average',
            category: '$course.category'
          }
        },
        { $sort: { enrollments: -1 } },
        { $limit: 10 }
      ]);

      // Application trends (simplified - would need daily tracking)
      const applicationTrends = await JobApplication.aggregate([
        {
          $match: {
            jobId: { $in: jobIds },
            appliedAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$appliedAt'
              }
            },
            applications: { $sum: 1 },
            views: { $sum: 1 } // Placeholder
          }
        },
        {
          $project: {
            date: '$_id',
            applications: 1,
            views: 1
          }
        },
        { $sort: { date: 1 } }
      ]);

      // Student engagement metrics
      const activeStudents = await Enrollment.countDocuments({
        courseId: { $in: courseIds },
        isActive: true,
        lastAccessedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      });

      const enrollmentStats = await Enrollment.aggregate([
        { $match: { courseId: { $in: courseIds }, isActive: true } },
        {
          $group: {
            _id: null,
            totalEnrollments: { $sum: 1 },
            completedCourses: {
              $sum: {
                $cond: [{ $ne: ['$completedAt', null] }, 1, 0]
              }
            },
            avgTimeSpent: { $avg: '$progress.timeSpent' }
          }
        }
      ]);

      const enrollmentData = enrollmentStats[0] || {
        totalEnrollments: 0,
        completedCourses: 0,
        avgTimeSpent: 0
      };

      const courseCompletionRate = enrollmentData.totalEnrollments > 0
        ? (enrollmentData.completedCourses / enrollmentData.totalEnrollments) * 100
        : 0;

      const certificateEarnRate = totalStudents > 0
        ? (totalCertificates / totalStudents) * 100
        : 0;

      const analyticsData = {
        overview: {
          totalJobs,
          totalApplications,
          totalCourses,
          totalStudents,
          totalEvents: 0, // Placeholder
          totalCertificates,
          applicationRate,
          avgTimeToHire: Math.round(avgTimeToHire),
          successRate
        },
        jobPerformance,
        coursePopularity,
        applicationTrends,
        studentEngagement: {
          totalActiveStudents: activeStudents,
          avgSessionDuration: Math.round(enrollmentData.avgTimeSpent / 60) || 0, // Convert to minutes
          courseCompletionRate,
          certificateEarnRate
        }
      };

      return NextResponse.json({
        success: true,
        data: analyticsData
      });
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch analytics data' },
        { status: 500 }
      );
    }
  });
}
