import mongoose, { Document, Schema } from 'mongoose';

export interface ICourse extends Document {
  _id: string;
  title: string;
  description: string;
  instructor: string;
  instructorId: string;
  category: string;
  subcategory?: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // in hours
  thumbnail: string;
  tags: string[];
  modules: {
    title: string;
    description: string;
    order: number;
    lessons: {
      title: string;
      type: 'video' | 'text' | 'quiz' | 'assignment';
      content: string; // URL for video, text content, or quiz data
      duration?: number; // in minutes
      order: number;
      isCompleted?: boolean;
    }[];
  }[];
  prerequisites: string[];
  learningOutcomes: string[];
  certification: {
    available: boolean;
    passingScore?: number;
    certificateTemplate?: string;
  };
  pricing: {
    isFree: boolean;
    price?: number;
    currency?: string;
  };
  enrollment: {
    totalStudents: number;
    maxStudents?: number;
    isOpen: boolean;
  };
  ratings: {
    average: number;
    totalRatings: number;
    breakdown: {
      5: number;
      4: number;
      3: number;
      2: number;
      1: number;
    };
  };
  isPublished: boolean;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const CourseSchema = new Schema<ICourse>({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
  },
  instructor: {
    type: String,
    required: true,
  },
  instructorId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  category: {
    type: String,
    required: true,
    enum: [
      'Space Technology',
      'Satellite Engineering',
      'AI in Space',
      'Cybersecurity',
      'Data Science',
      'Robotics',
      'Astrophysics',
      'Space Law',
      'Business & Entrepreneurship',
      'Other'
    ],
  },
  subcategory: String,
  level: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    required: true,
  },
  duration: {
    type: Number,
    required: true,
  },
  thumbnail: {
    type: String,
    required: true,
  },
  tags: [String],
  modules: [{
    title: { type: String, required: true },
    description: String,
    order: { type: Number, required: true },
    lessons: [{
      title: { type: String, required: true },
      type: {
        type: String,
        enum: ['video', 'text', 'quiz', 'assignment'],
        required: true,
      },
      content: { type: String, required: true },
      duration: Number,
      order: { type: Number, required: true },
    }],
  }],
  prerequisites: [String],
  learningOutcomes: [String],
  certification: {
    available: { type: Boolean, default: false },
    passingScore: Number,
    certificateTemplate: String,
  },
  pricing: {
    isFree: { type: Boolean, default: true },
    price: Number,
    currency: { type: String, default: 'USD' },
  },
  enrollment: {
    totalNerds: { type: Number, default: 0 },
    maxNerds: Number,
    isOpen: { type: Boolean, default: true },
  },
  ratings: {
    average: { type: Number, default: 0 },
    totalRatings: { type: Number, default: 0 },
    breakdown: {
      5: { type: Number, default: 0 },
      4: { type: Number, default: 0 },
      3: { type: Number, default: 0 },
      2: { type: Number, default: 0 },
      1: { type: Number, default: 0 },
    },
  },
  isPublished: { type: Boolean, default: false },
  publishedAt: Date,
}, {
  timestamps: true,
});

// Indexes for better query performance
CourseSchema.index({ category: 1 });
CourseSchema.index({ level: 1 });
CourseSchema.index({ tags: 1 });
CourseSchema.index({ isPublished: 1 });
CourseSchema.index({ 'ratings.average': -1 });
CourseSchema.index({ 'enrollment.totalNerds': -1 });

const Course = mongoose.models.Course || mongoose.model<ICourse>('Course', CourseSchema);

export default Course;
export { Course };
